/* Category Service Selector Styles */
.categorySelector {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 500px;
}

.header {
  margin-bottom: 2rem;
  text-align: center;
}

.header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.searchInput {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #64748b;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Categories Grid */
.categoriesGrid h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.categoryCards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.categoryCard {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.categoryCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.categoryCard h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.categoryCard p {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0 0 1rem 0;
}

.servicePreview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.serviceTag {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  backdrop-filter: blur(5px);
}

.moreTag {
  background: rgba(255, 255, 255, 0.3);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-style: italic;
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.backBtn {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.backBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
}

.breadcrumb h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* Services Grid */
.serviceCards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.serviceCard {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.serviceCard:hover {
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.serviceHeader h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.serviceDescription {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.tierOptions {
  text-align: center;
}

.tierLabel {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
}

.viewTiersBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  width: 100%;
}

.viewTiersBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.singleServiceOption {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.serviceDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
}

.price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #10b981;
}

.duration {
  font-size: 0.875rem;
  color: #64748b;
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.serviceActions {
  display: flex;
  gap: 0.75rem;
}

.selectBtn, .addToCartBtn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.selectBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.selectBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.addToCartBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.addToCartBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

/* Tier Selection */
.tierGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.tierCard {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.tierCard:hover {
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.tierHeader h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.tierDescription {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.tierDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
  position: relative;
}

.defaultBadge {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.tierActions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .categoryCards, .serviceCards, .tierGrid {
    grid-template-columns: 1fr;
  }
  
  .categorySelector {
    padding: 1rem;
  }
  
  .serviceActions, .tierActions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .breadcrumb {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
