import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { QuickExportButton } from '@/components/admin/ExportButton';
import styles from '@/styles/admin/Services.module.css';

/**
 * Services Management Page
 * 
 * This page provides a comprehensive interface for managing service catalog,
 * including pricing, categories, and service details.
 */
export default function ServicesManagement() {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Load services from API
  const loadServices = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/services', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch services');
      }

      const data = await response.json();
      setServices(data.services || []);
      setFilteredServices(data.services || []);
    } catch (error) {
      console.error('Error loading services:', error);
      setServices([]);
      setFilteredServices([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && user) {
      loadServices();
    }
  }, [authLoading, user]);

  // Filter and sort services
  useEffect(() => {
    let filtered = services;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(service => service.category === categoryFilter);
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(service => service.status === statusFilter);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'price':
          return (b.price || 0) - (a.price || 0);
        case 'duration':
          return (b.duration || 0) - (a.duration || 0);
        default:
          return 0;
      }
    });

    setFilteredServices(filtered);
  }, [services, searchTerm, categoryFilter, statusFilter, sortBy]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDuration = (minutes) => {
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Loading Services - Ocean Soul Sparkles Admin</title>
        </Head>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading services...</p>
        </div>
      </>
    );
  }

  if (!user) {
    return null; // Will redirect to login via useAuth
  }

  return (
    <>
      <Head>
        <title>Services Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage service catalog and pricing" />
      </Head>

      <div className={styles.servicesContainer}>
        <header className={styles.header}>
          <h1 className={styles.title}>Services Management</h1>
          <div className={styles.headerActions}>
            <QuickExportButton
              data={filteredServices}
              type="services"
              className={styles.exportBtn}
            />
            <Link href="/admin/services/new" className={styles.newServiceBtn}>
              + Add Service
            </Link>
          </div>
        </header>

        <div className={styles.controlsPanel}>
          <div className={styles.searchSection}>
            <input
              type="text"
              placeholder="Search services by name, category, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <div className={styles.filtersSection}>
            <div className={styles.filterGroup}>
              <label>Category:</label>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className={styles.filterSelect}
              >
                <option value="all">All Categories</option>
                <option value="">Uncategorized</option>
                <option value="Airbrush">Airbrush</option>
                <option value="Body Painting">Body Painting</option>
                <option value="braiding">Braiding</option>
                <option value="Face Painting">Face Painting</option>
                <option value="Glitter & Gems">Glitter & Gems</option>
                <option value="Hair & Braiding">Hair & Braiding</option>
                <option value="special">Special</option>
                <option value="Special">Special</option>
                <option value="undefined">Undefined</option>
              </select>
            </div>

            <div className={styles.filterGroup}>
              <label>Status:</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className={styles.filterSelect}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="archived">Archived</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            <div className={styles.filterGroup}>
              <label>Sort by:</label>
              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
                className={styles.filterSelect}
              >
                <option value="name">Name</option>
                <option value="category">Category</option>
                <option value="price">Price</option>
                <option value="duration">Duration</option>
              </select>
            </div>
          </div>
        </div>

        <div className={styles.servicesContent}>
          {filteredServices.length === 0 ? (
            <div className={styles.emptyState}>
              <h3>No services found</h3>
              <p>
                {services.length === 0 
                  ? "Get started by adding your first service to the catalog."
                  : "Try adjusting your search or filter criteria."
                }
              </p>
              <Link href="/admin/services/new" className={styles.addFirstBtn}>
                Add First Service
              </Link>
            </div>
          ) : (
            <div className={styles.servicesGrid}>
              {filteredServices.map((service) => (
                <div key={service.id} className={styles.serviceCard}>
                  <div className={styles.cardHeader}>
                    <h3 className={styles.serviceName}>{service.name}</h3>
                    <div className={styles.badges}>
                      <span className={styles.categoryBadge}>{service.category || 'Uncategorized'}</span>
                      <span className={`${styles.statusBadge} ${styles[service.status || 'active']}`}>
                        {(service.status || 'active').toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className={styles.cardBody}>
                    {service.description && (
                      <p className={styles.description}>{service.description}</p>
                    )}

                    <div className={styles.serviceDetails}>
                      <div className={styles.priceInfo}>
                        <span className={styles.label}>Price:</span>
                        <span className={styles.value}>
                          {service.price ? formatCurrency(service.price) : 'Contact for pricing'}
                        </span>
                      </div>
                      
                      {service.duration && (
                        <div className={styles.durationInfo}>
                          <span className={styles.label}>Duration:</span>
                          <span className={styles.value}>{formatDuration(service.duration)}</span>
                        </div>
                      )}
                      
                      {service.requirements && (
                        <div className={styles.requirementsInfo}>
                          <span className={styles.label}>Requirements:</span>
                          <span className={styles.value}>{service.requirements}</span>
                        </div>
                      )}
                    </div>

                    {service.images && service.images.length > 0 && (
                      <div className={styles.serviceImages}>
                        <div className={styles.imageCount}>
                          {service.images.length} image{service.images.length > 1 ? 's' : ''}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className={styles.cardActions}>
                    <Link href={`/admin/services/${service.id}`} className={styles.viewBtn}>
                      View Details
                    </Link>
                    <Link href={`/admin/services/${service.id}/edit`} className={styles.editBtn}>
                      Edit
                    </Link>
                    <button 
                      className={styles.toggleBtn}
                      title={service.active ? 'Disable service' : 'Enable service'}
                    >
                      {service.active ? 'Disable' : 'Enable'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
