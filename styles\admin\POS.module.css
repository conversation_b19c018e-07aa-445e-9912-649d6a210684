/* POS System Styles */

.posContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.backButton {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.backButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(100, 116, 139, 0.4);
}

.posLayout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
}

/* Services Panel */
.servicesPanel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
}

.servicesPanel h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.serviceCategories {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.categorySection {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1.5rem;
}

.categorySection:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.categoryTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.serviceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.serviceCard {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.serviceCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
  border-color: #667eea;
}

.serviceName {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.servicePrice {
  font-size: 1.2rem;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 0.25rem;
}

.serviceDuration {
  font-size: 0.8rem;
  color: #64748b;
}

/* Cart Panel */
.cartPanel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 120px);
}

.cartPanel h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.customerSection {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.customerSection h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 1rem 0;
}

.customerInfo {
  background: #f1f5f9;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.customerInfo p {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
}

.customerInfo p:last-of-type {
  margin-bottom: 1rem;
}

.changeCustomerBtn {
  background: #64748b;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.changeCustomerBtn:hover {
  background: #475569;
}

.customerActions {
  display: flex;
  gap: 0.5rem;
}

.addCustomerBtn, .walkInBtn {
  flex: 1;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.walkInBtn {
  background: linear-gradient(135deg, #64748b, #475569);
}

.addCustomerBtn:hover, .walkInBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.cartItems {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}

.emptyCart {
  text-align: center;
  color: #64748b;
  font-style: italic;
  padding: 2rem;
}

.cartItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  margin-bottom: 0.5rem;
  border-radius: 8px;
}

.itemInfo {
  flex: 1;
}

.itemName {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.itemPrice {
  color: #10b981;
  font-weight: 500;
}

.itemControls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantityBtn {
  background: #e2e8f0;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  color: #475569;
  transition: all 0.2s ease;
}

.quantityBtn:hover {
  background: #cbd5e1;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
  color: #1e293b;
}

.removeBtn {
  background: #ef4444;
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.removeBtn:hover {
  background: #dc2626;
}

.paymentSection {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.total {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  text-align: center;
  margin-bottom: 1rem;
}

.paymentMethods {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  justify-content: center;
}

.paymentMethods label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: #475569;
  font-weight: 500;
}

.processPaymentBtn {
  width: 100%;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.processPaymentBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.processPaymentBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* POS Content Layout */
.posContent {
  padding: 2rem;
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.servicesSection {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex: 1;
  overflow-y: auto;
}

.sectionHeader {
  margin-bottom: 2rem;
  text-align: center;
}

.sectionHeader h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.sectionHeader p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
}

.searchInput {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  padding: 0.5rem;
}

.serviceCategory {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.serviceDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
}

.price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #10b981;
}

.duration {
  font-size: 0.875rem;
  color: #64748b;
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.serviceActions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
}

.bookServiceBtn, .addToCartBtn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.bookServiceBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.bookServiceBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.addToCartBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.addToCartBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.addToCartBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Quick Cart Styles */
.quickCart {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 1rem;
}

.quickCart h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.cartTotal {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  text-align: center;
  margin: 1rem 0;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 8px;
}

.cartActions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.clearBtn, .quickCheckoutBtn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.clearBtn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.clearBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.quickCheckoutBtn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.quickCheckoutBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.quickCheckoutBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.quantityControls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.itemTotal {
  font-weight: 600;
  color: #10b981;
  min-width: 80px;
  text-align: right;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .posLayout {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .cartPanel {
    max-height: none;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .serviceGrid, .servicesGrid {
    grid-template-columns: 1fr;
  }

  .posContent {
    padding: 1rem;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .serviceActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .customerActions {
    flex-direction: column;
  }
}

/* Tip Management Styles */
.tipContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.tipContent {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.tipHeader {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
  position: relative;
}

.tipHeader h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.tipHeader p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

.tipCancelButton {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.tipCancelButton:hover {
  background: #dc2626;
}

.tipError {
  background: #fee2e2;
  color: #991b1b;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #fca5a5;
}

.tipTypeSelector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.tipTypeButton {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tipTypeButton:hover {
  border-color: #3b82f6;
}

.tipTypeButton.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.tipPercentageOptions h4,
.tipCustomAmount h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.tipPercentageGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.tipPercentageButton {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  font-weight: 500;
}

.tipPercentageButton:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.tipPercentageButton.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.tipAmount {
  display: block;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

.customPercentageInput,
.customAmountInput {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.customPercentageInput label {
  font-weight: 500;
  color: #374151;
}

.customPercentageInput input,
.customAmountInput input {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
}

.customPercentageInput input:focus,
.customAmountInput input:focus {
  outline: none;
  border-color: #3b82f6;
}

.currencySymbol {
  font-size: 1.2rem;
  font-weight: 600;
  color: #374151;
}

.tipSummary {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border: 1px solid #e2e8f0;
}

.tipSummaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.tipSummaryRow:last-child {
  margin-bottom: 0;
}

.tipSummaryRow.total {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  padding-top: 0.5rem;
  border-top: 2px solid #e2e8f0;
  margin-top: 0.5rem;
}

.tipActions {
  margin-top: 2rem;
}

.tipConfirmButton {
  width: 100%;
  padding: 1rem 2rem;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tipConfirmButton:hover:not(:disabled) {
  background: #059669;
}

.tipConfirmButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}
