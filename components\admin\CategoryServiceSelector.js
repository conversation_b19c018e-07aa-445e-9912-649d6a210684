import { useState, useEffect } from 'react';
import styles from '../../styles/admin/CategoryServiceSelector.module.css';

export default function CategoryServiceSelector({ onServiceSelect, onAddToCart }) {
  const [services, setServices] = useState([]);
  const [pricingTiers, setPricingTiers] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchServicesAndTiers();
  }, []);

  const fetchServicesAndTiers = async () => {
    try {
      setLoading(true);
      
      // Fetch services
      const servicesResponse = await fetch('/api/admin/services');
      const servicesData = await servicesResponse.json();
      
      // Fetch pricing tiers
      const tiersResponse = await fetch('/api/admin/service-pricing-tiers');
      const tiersData = await tiersResponse.json();
      
      // Filter for active POS-visible services
      const activeServices = servicesData.filter(service => 
        service.status === 'active' && service.visible_on_pos === true
      );
      
      setServices(activeServices);
      setPricingTiers(tiersData);
    } catch (error) {
      console.error('Error fetching services and tiers:', error);
    } finally {
      setLoading(false);
    }
  };

  // Group services by category
  const servicesByCategory = services.reduce((acc, service) => {
    const category = service.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(service);
    return acc;
  }, {});

  // Get tiers for a specific service
  const getTiersForService = (serviceId) => {
    return pricingTiers
      .filter(tier => tier.service_id === serviceId)
      .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
  };

  // Filter categories based on search
  const filteredCategories = Object.keys(servicesByCategory).filter(category => {
    if (!searchTerm) return true;
    
    const categoryMatches = category.toLowerCase().includes(searchTerm.toLowerCase());
    const servicesMatch = servicesByCategory[category].some(service =>
      service.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return categoryMatches || servicesMatch;
  });

  const handleServiceSelect = (service, tier = null) => {
    const serviceData = {
      id: service.id,
      name: service.name,
      category: service.category,
      price: tier ? tier.price : service.price,
      duration: tier ? tier.duration : service.duration,
      description: tier ? tier.description : service.description,
      tier_name: tier ? tier.name : null,
      tier_id: tier ? tier.id : null
    };
    
    if (onServiceSelect) {
      onServiceSelect(serviceData);
    }
  };

  const handleAddToCart = (service, tier = null) => {
    const serviceData = {
      id: service.id,
      name: service.name,
      category: service.category,
      price: tier ? tier.price : service.price,
      duration: tier ? tier.duration : service.duration,
      description: tier ? tier.description : service.description,
      tier_name: tier ? tier.name : null,
      tier_id: tier ? tier.id : null
    };
    
    if (onAddToCart) {
      onAddToCart(serviceData);
    }
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading services...</p>
      </div>
    );
  }

  return (
    <div className={styles.categorySelector}>
      <div className={styles.header}>
        <h2>Select Service</h2>
        <input
          type="text"
          placeholder="Search services or categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className={styles.searchInput}
        />
      </div>

      {!selectedCategory && (
        <div className={styles.categoriesGrid}>
          <h3>Service Categories</h3>
          <div className={styles.categoryCards}>
            {filteredCategories.map(category => (
              <div
                key={category}
                className={styles.categoryCard}
                onClick={() => setSelectedCategory(category)}
              >
                <h4>{category}</h4>
                <p>{servicesByCategory[category].length} service{servicesByCategory[category].length !== 1 ? 's' : ''}</p>
                <div className={styles.servicePreview}>
                  {servicesByCategory[category].slice(0, 3).map(service => (
                    <span key={service.id} className={styles.serviceTag}>
                      {service.name}
                    </span>
                  ))}
                  {servicesByCategory[category].length > 3 && (
                    <span className={styles.moreTag}>+{servicesByCategory[category].length - 3} more</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {selectedCategory && !selectedService && (
        <div className={styles.servicesGrid}>
          <div className={styles.breadcrumb}>
            <button onClick={() => setSelectedCategory(null)} className={styles.backBtn}>
              ← Back to Categories
            </button>
            <h3>{selectedCategory}</h3>
          </div>
          
          <div className={styles.serviceCards}>
            {servicesByCategory[selectedCategory].map(service => {
              const tiers = getTiersForService(service.id);
              const hasMultipleTiers = tiers.length > 1;
              
              return (
                <div key={service.id} className={styles.serviceCard}>
                  <div className={styles.serviceHeader}>
                    <h4>{service.name}</h4>
                    {service.description && (
                      <p className={styles.serviceDescription}>{service.description}</p>
                    )}
                  </div>
                  
                  {hasMultipleTiers ? (
                    <div className={styles.tierOptions}>
                      <p className={styles.tierLabel}>Choose option:</p>
                      <button
                        onClick={() => setSelectedService(service)}
                        className={styles.viewTiersBtn}
                      >
                        View {tiers.length} Options →
                      </button>
                    </div>
                  ) : (
                    <div className={styles.singleServiceOption}>
                      <div className={styles.serviceDetails}>
                        <span className={styles.price}>${service.price}</span>
                        <span className={styles.duration}>{service.duration} min</span>
                      </div>
                      <div className={styles.serviceActions}>
                        <button
                          onClick={() => handleServiceSelect(service)}
                          className={styles.selectBtn}
                        >
                          Select
                        </button>
                        <button
                          onClick={() => handleAddToCart(service)}
                          className={styles.addToCartBtn}
                        >
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {selectedService && (
        <div className={styles.tierSelection}>
          <div className={styles.breadcrumb}>
            <button onClick={() => setSelectedService(null)} className={styles.backBtn}>
              ← Back to {selectedCategory}
            </button>
            <h3>{selectedService.name}</h3>
          </div>
          
          <div className={styles.tierGrid}>
            {getTiersForService(selectedService.id).map(tier => (
              <div key={tier.id} className={styles.tierCard}>
                <div className={styles.tierHeader}>
                  <h4>{tier.name}</h4>
                  {tier.description && (
                    <p className={styles.tierDescription}>{tier.description}</p>
                  )}
                </div>
                
                <div className={styles.tierDetails}>
                  <span className={styles.price}>${tier.price}</span>
                  <span className={styles.duration}>{tier.duration} min</span>
                  {tier.is_default && (
                    <span className={styles.defaultBadge}>Recommended</span>
                  )}
                </div>
                
                <div className={styles.tierActions}>
                  <button
                    onClick={() => handleServiceSelect(selectedService, tier)}
                    className={styles.selectBtn}
                  >
                    Select
                  </button>
                  <button
                    onClick={() => handleAddToCart(selectedService, tier)}
                    className={styles.addToCartBtn}
                  >
                    Add to Cart
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
