import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyMFAAndLogin } from '../../../lib/auth/admin-auth';
import { rateLimitCheck } from '../../../lib/security/rate-limiting';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Rate limiting check
    const rateLimitResult = await rateLimitCheck(req as any);
    if (!rateLimitResult.allowed) {
      return res.status(429).json({ 
        error: 'Too many MFA attempts. Please try again later.',
        resetTime: rateLimitResult.resetTime
      });
    }

    const { userId, mfaCode } = req.body;

    // Validate input
    if (!userId || !mfaCode) {
      return res.status(400).json({ error: 'User ID and MFA code are required' });
    }

    // Validate MFA code format
    if (!/^\d{6}$/.test(mfaCode)) {
      return res.status(400).json({ error: 'MFA code must be 6 digits' });
    }

    // Get client IP for audit logging
    const clientIP = req.headers['x-forwarded-for'] as string || 
                    req.headers['x-real-ip'] as string || 
                    req.connection.remoteAddress || 
                    'unknown';

    // Verify MFA and complete login
    const loginResult = await verifyMFAAndLogin(userId, mfaCode, clientIP);

    if (!loginResult.success) {
      return res.status(401).json({ error: loginResult.error });
    }

    // Set secure cookie with environment-appropriate settings
    if (loginResult.token) {
      const isProduction = process.env.NODE_ENV === 'production';
      const secureFlag = isProduction ? 'Secure; ' : '';

      res.setHeader('Set-Cookie', [
        `admin-token=${loginResult.token}; HttpOnly; ${secureFlag}SameSite=Strict; Path=/; Max-Age=28800` // 8 hours
      ]);
    }

    return res.status(200).json({
      success: true,
      token: loginResult.token,
      user: loginResult.user
    });

  } catch (error) {
    console.error('MFA verification API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
