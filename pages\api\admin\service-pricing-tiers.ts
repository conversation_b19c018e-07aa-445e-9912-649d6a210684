import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const { data: tiers, error } = await supabase
        .from('service_pricing_tiers')
        .select(`
          id,
          service_id,
          name,
          description,
          price,
          duration,
          is_default,
          sort_order,
          created_at,
          updated_at
        `)
        .order('service_id')
        .order('sort_order');

      if (error) {
        console.error('Error fetching service pricing tiers:', error);
        return res.status(500).json({ error: 'Failed to fetch service pricing tiers' });
      }

      // Transform data to ensure proper types
      const transformedTiers = (tiers || []).map(tier => ({
        id: tier.id,
        service_id: tier.service_id,
        name: tier.name,
        description: tier.description,
        price: parseFloat(tier.price || '0'),
        duration: tier.duration,
        is_default: tier.is_default,
        sort_order: tier.sort_order || 0,
        created_at: tier.created_at,
        updated_at: tier.updated_at
      }));

      res.status(200).json(transformedTiers);
    } catch (error) {
      console.error('Error in service pricing tiers API:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else if (req.method === 'POST') {
    try {
      const { service_id, name, description, price, duration, is_default, sort_order } = req.body;

      // Validate required fields
      if (!service_id || !name || !price || !duration) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const { data: tier, error } = await supabase
        .from('service_pricing_tiers')
        .insert({
          service_id,
          name,
          description,
          price: parseFloat(price),
          duration: parseInt(duration),
          is_default: is_default || false,
          sort_order: sort_order || 0
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating service pricing tier:', error);
        return res.status(500).json({ error: 'Failed to create service pricing tier' });
      }

      res.status(201).json(tier);
    } catch (error) {
      console.error('Error in service pricing tiers API:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else if (req.method === 'PUT') {
    try {
      const { id, name, description, price, duration, is_default, sort_order } = req.body;

      if (!id) {
        return res.status(400).json({ error: 'Tier ID is required' });
      }

      const updateData: any = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (price !== undefined) updateData.price = parseFloat(price);
      if (duration !== undefined) updateData.duration = parseInt(duration);
      if (is_default !== undefined) updateData.is_default = is_default;
      if (sort_order !== undefined) updateData.sort_order = sort_order;
      updateData.updated_at = new Date().toISOString();

      const { data: tier, error } = await supabase
        .from('service_pricing_tiers')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating service pricing tier:', error);
        return res.status(500).json({ error: 'Failed to update service pricing tier' });
      }

      res.status(200).json(tier);
    } catch (error) {
      console.error('Error in service pricing tiers API:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else if (req.method === 'DELETE') {
    try {
      const { id } = req.query;

      if (!id) {
        return res.status(400).json({ error: 'Tier ID is required' });
      }

      const { error } = await supabase
        .from('service_pricing_tiers')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting service pricing tier:', error);
        return res.status(500).json({ error: 'Failed to delete service pricing tier' });
      }

      res.status(200).json({ message: 'Service pricing tier deleted successfully' });
    } catch (error) {
      console.error('Error in service pricing tiers API:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
    res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}
